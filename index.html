<style>
.overflow_hide {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}
</style>

<div class="bt-form">
	<div class='plugin_version'></div>
	<div class="bt-w-main">
		<div class="bt-w-menu">
			<p class="bgw" onclick="pluginService('goedge-happy');">服务</p>
			<p onclick="pluginInitD('goedge-happy');">自启动</p>
			<p onclick="pluginConfigTpl('goedge-happy',$('.plugin_version').attr('version'));">配置修改</p>
			<p onclick="pluginLogs('goedge-happy',null,'run_log');">运行日志</p>
			<p onclick="goedgeReadme();">相关说明</p>
			
		</div>
		<div class="bt-w-con pd15">
			<div class="soft-man-con" style="height: 520px; overflow: auto;"></div>
		</div>
	</div>
</div>
<script type="text/javascript">
$.getScript( "/plugins/file?name=goedge-happy&f=js/goedge-admin.js", function(){
	pluginService('goedge-happy', $('.plugin_version').attr('version'));
});
</script>